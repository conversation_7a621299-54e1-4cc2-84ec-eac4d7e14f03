package main

import (
	"context"
	"errors"
	"log/slog"
	"sync"

	"github.com/BhaumikTalwar/Amrita/models/dto"
	gameapi "github.com/BhaumikTalwar/Amrita/service/GameApi"
)

type PlayerState int

const (
	PlayerStateActive PlayerState = iota
	PlayerStateLeft
)

type PlayerInfo struct {
	Username     string
	SessionToken string
	Symbol       string
	State        PlayerState
}

type TicTacToe struct{}

func NewGame() gameapi.Game {
	return &TicTacToe{}
}

type TicTacToeInstance struct {
	roomID string
	board  [7][7]string
	config gameapi.GameConfig
	mu     sync.Mutex

	playerInfo  map[string]*PlayerInfo
	currentTurn string
	isGameOver  bool
	winner      string
}

func (g *TicTacToe) NewInstance(config gameapi.GameConfig, roomID string) gameapi.GameInstance {
	if err := g.ValidateConfig(config); err != nil {
		slog.Error("Error Cant initialize the Instance Config not valid", slog.Any("Error", err))
		return nil
	}
	return &TicTacToeInstance{
		roomID:     roomID,
		config:     config,
		playerInfo: make(map[string]*PlayerInfo),
		isGameOver: false,
	}
}

func (g *TicTacToe) ValidateConfig(config gameapi.GameConfig) error {
	if size, ok := config["boardSize"].(float64); ok {
		if int(size) != 7 {
			return errors.New("boardSize must be 7")
		}
	}
	if winLength, ok := config["winLength"].(float64); ok {
		w := int(winLength)
		if w < 3 || w > 7 {
			return errors.New("winLength must be an integer between 3 and 7")
		}
	}
	return nil
}

func (g *TicTacToeInstance) HandlePlayerJoin(ctx context.Context, playerID string, sessionId string, playerData interface{}) error {
	g.mu.Lock()
	defer g.mu.Unlock()

	if len(g.playerInfo) >= 2 {
		return errors.New("game is full")
	}

	pData, ok := playerData.(map[string]interface{})
	if !ok {
		return errors.New("invalid player data format")
	}
	name, ok := pData["username"].(string)
	if !ok {
		return errors.New("username not provided")
	}

	symbol := "X"
	if len(g.playerInfo) == 1 {
		for _, info := range g.playerInfo {
			if info.Symbol == "X" {
				symbol = "O"
			}
		}
	}

	g.playerInfo[playerID] = &PlayerInfo{
		Username:     name,
		SessionToken: sessionId,
		Symbol:       symbol,
		State:        PlayerStateActive,
	}

	if len(g.playerInfo) == 2 {
		g.currentTurn = "X"
	}

	return nil
}

func (g *TicTacToeInstance) HandlePlayerAction(ctx context.Context, playerID string, action interface{}) error {
	g.mu.Lock()
	defer g.mu.Unlock()

	if g.isGameOver {
		return errors.New("game is over")
	}

	info, exists := g.playerInfo[playerID]
	if !exists || info.State != PlayerStateActive {
		return errors.New("player not active in game")
	}
	if info.Symbol != g.currentTurn {
		return errors.New("not your turn")
	}

	move, ok := action.(map[string]interface{})
	if !ok {
		return errors.New("invalid action format")
	}

	row, ok := move["row"].(float64)
	if !ok {
		return errors.New("invalid row")
	}
	col, ok := move["col"].(float64)
	if !ok {
		return errors.New("invalid col")
	}

	r, c := int(row), int(col)
	if r < 0 || r >= 7 || c < 0 || c >= 7 {
		return errors.New("move out of bounds")
	}
	if g.board[r][c] != "" {
		return errors.New("cell already occupied")
	}

	g.board[r][c] = info.Symbol

	if g.checkWin(r, c, info.Symbol) {
		g.isGameOver = true
		g.winner = info.Symbol
		return nil
	}

	if g.checkDraw() {
		g.isGameOver = true
		return nil
	}

	if info.Symbol == "X" {
		g.currentTurn = "O"
	} else {
		g.currentTurn = "X"
	}

	return nil
}

func (g *TicTacToeInstance) HandlePlayerLeave(ctx context.Context, playerID string) error {
	g.mu.Lock()
	defer g.mu.Unlock()

	leaverInfo, exists := g.playerInfo[playerID]
	if !exists || leaverInfo.State == PlayerStateLeft {
		return nil
	}

	g.isGameOver = true
	leaverInfo.State = PlayerStateLeft

	for _, info := range g.playerInfo {
		if info.State == PlayerStateActive {
			g.winner = info.Symbol
			break
		}
	}

	return nil
}

func (g *TicTacToeInstance) CalculateFinalResults(lobby_price float64, reason dto.GameEndReason, faultingPlayerID *string) []dto.PlayerResult {
	g.mu.Lock()
	defer g.mu.Unlock()

	if !g.isGameOver && reason != dto.GameEndReasonInsufficientPlayers {
		return nil
	}

	results := make([]dto.PlayerResult, 0, len(g.playerInfo))

	for playerID, info := range g.playerInfo {
		result := dto.PlayerResult{
			UserID:       playerID,
			UserName:     info.Username,
			SessionToken: info.SessionToken,
			Status:       dto.PlayerStatusUnknown,
			Metadata:     map[string]interface{}{"symbol": info.Symbol},
		}

		if reason == dto.GameEndReasonInsufficientPlayers {
			result.Rank = 0
			result.CreditInfo = dto.PlayerCreditInfo{Amount: lobby_price, Reason: "Insufficient Players"}
		} else if info.State == PlayerStateLeft {
			result.Status = dto.PlayerStatusForfeit
			result.Rank = 2
			result.CreditInfo = dto.PlayerCreditInfo{Amount: 0, Reason: "Forfeited game"}

		} else if g.winner != "" {
			if g.winner == info.Symbol {
				result.Status = dto.PlayerStatusWin
				result.Rank = 1
				result.CreditInfo = dto.PlayerCreditInfo{Amount: 2 * lobby_price, Reason: "Winner"}
			} else {
				result.Status = dto.PlayerStatusLose
				result.Rank = 2
				result.CreditInfo = dto.PlayerCreditInfo{Amount: 0, Reason: "Lost"}
			}
		} else if g.isGameOver {
			result.Status = dto.PlayerStatusDraw
			result.Rank = 1
			result.CreditInfo = dto.PlayerCreditInfo{Amount: 0, Reason: "Draw"}
		}

		results = append(results, result)
	}

	return results
}

func (g *TicTacToeInstance) GetGameState(playerID string) interface{} {
	g.mu.Lock()
	defer g.mu.Unlock()

	info, exists := g.playerInfo[playerID]
	if !exists {
		return g.GetPublicState()
	}

	return map[string]interface{}{
		"board":       g.board,
		"symbol":      info.Symbol,
		"currentTurn": g.currentTurn,
		"players":     g.buildPlayerNameMap(),
		"isGameOver":  g.isGameOver,
		"winner":      g.winner,
	}
}

func (g *TicTacToeInstance) GetPublicState() interface{} {
	g.mu.Lock()
	defer g.mu.Unlock()

	return map[string]interface{}{
		"board":       g.board,
		"currentTurn": g.currentTurn,
		"players":     g.buildPlayerNameMap(),
		"isGameOver":  g.isGameOver,
		"winner":      g.winner,
	}
}

func (g *TicTacToeInstance) IsGameOver() bool {
	g.mu.Lock()
	defer g.mu.Unlock()
	return g.isGameOver
}

func (g *TicTacToeInstance) GetWinner() interface{} {
	g.mu.Lock()
	defer g.mu.Unlock()
	return g.winner
}

func (g *TicTacToeInstance) Cleanup() error {
	g.mu.Lock()
	defer g.mu.Unlock()

	g.board = [7][7]string{}
	g.playerInfo = make(map[string]*PlayerInfo)
	g.currentTurn = ""
	g.isGameOver = false
	g.winner = ""
	return nil
}

func (g *TicTacToeInstance) buildPlayerNameMap() map[string]string {
	names := make(map[string]string)
	for _, info := range g.playerInfo {
		if info.State == PlayerStateActive {
			names[info.Symbol] = info.Username
		}
	}
	return names
}

func (g *TicTacToeInstance) checkWin(row, col int, symbol string) bool {
	winLength := 5 // Default win length
	if wl, ok := g.config["winLength"].(float64); ok {
		winLength = int(wl)
	}

	dirs := [][2]int{{0, 1}, {1, 0}, {1, 1}, {1, -1}}

	for _, dir := range dirs {
		count := 1

		for i := 1; i < winLength; i++ {
			r, c := row+i*dir[0], col+i*dir[1]
			if r >= 0 && r < 7 && c >= 0 && c < 7 && g.board[r][c] == symbol {
				count++
			} else {
				break
			}
		}

		for i := 1; i < winLength; i++ {
			r, c := row-i*dir[0], col-i*dir[1]
			if r >= 0 && r < 7 && c >= 0 && c < 7 && g.board[r][c] == symbol {
				count++
			} else {
				break
			}
		}

		if count >= winLength {
			return true
		}
	}
	return false
}

func (g *TicTacToeInstance) checkDraw() bool {
	for i := 0; i < 7; i++ {
		for j := 0; j < 7; j++ {
			if g.board[i][j] == "" {
				return false
			}
		}
	}
	return true
}
