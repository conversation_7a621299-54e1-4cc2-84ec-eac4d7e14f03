<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>GamyDay Games - T<PERSON></title>
    <script src="https://cdn.tailwindcss.com"></script>
    <script src="https://cdn.jsdelivr.net/npm/canvas-confetti@1.9.2/dist/confetti.browser.min.js"></script>
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link
        href="https://fonts.googleapis.com/css2?family=Fredoka:wght@400;600;700&family=Inter:wght@400;500;700&display=swap"
        rel="stylesheet">
    <style>
        body {
            font-family: 'Inter', sans-serif;
            margin: 0;
            overflow: hidden;
            background-color: #0a0a1a;
        }

        .font-fredoka {
            font-family: 'Fredoka', sans-serif;
        }

        @keyframes float {

            0%,
            100% {
                transform: translateY(0);
            }

            50% {
                transform: translateY(-10px);
            }
        }

        .logo-float {
            animation: float 4s ease-in-out infinite;
        }

        @keyframes shine {
            0% {
                background-position: 200% 0;
            }

            100% {
                background-position: -200% 0;
            }
        }

        .progress-bar-shine::after {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: linear-gradient(120deg, rgba(255, 255, 255, 0) 40%, rgba(255, 255, 255, 0.5) 50%, rgba(255, 255, 255, 0) 60%);
            background-size: 200% 100%;
            animation: shine 2s infinite linear;
            z-index: 1;
        }

        @keyframes fadeInUp {
            from {
                opacity: 0;
                transform: translateY(30px);
            }

            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        .fade-in-up {
            animation: fadeInUp 1s ease-out forwards;
        }

        @keyframes fadeOut {
            from {
                opacity: 1;
            }

            to {
                opacity: 0;
            }
        }

        .fade-out {
            animation: fadeOut 0.5s ease-out forwards;
        }

        .text-glow {
            text-shadow: 0 0 8px rgba(255, 255, 255, 0.3), 0 0 20px rgba(255, 255, 255, 0.1);
        }

        .glow-bar {
            box-shadow: 0 0 12px rgba(255, 186, 60, 0.5), 0 0 20px rgba(255, 115, 40, 0.3);
        }

        #loadingScreen {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            z-index: 1000;
        }

        @keyframes cell-pop-in {
            from {
                transform: scale(0.5);
                opacity: 0;
            }

            to {
                transform: scale(1);
                opacity: 1;
            }
        }

        @keyframes modalPop {
            from {
                transform: scale(0.7);
                opacity: 0;
            }

            to {
                transform: scale(1);
                opacity: 1;
            }
        }

        @keyframes pulse {

            0%,
            100% {
                opacity: 1;
            }

            50% {
                opacity: 0.6;
            }
        }

        #gameUI {
            visibility: hidden;
            opacity: 0;
            transition: opacity 0.5s ease-in, visibility 0.5s;
            background: radial-gradient(ellipse at center, #101827, #0a0a1a);
        }

        #gameUI.active {
            visibility: visible;
            opacity: 1;
        }

        .text-glow-cyan {
            text-shadow: 0 0 8px rgba(56, 189, 248, 0.7);
        }

        .text-glow-orange {
            text-shadow: 0 0 8px rgba(249, 115, 22, 0.7);
        }

        #gameBoard {
            display: grid;
            grid-template-columns: repeat(7, 1fr);
            gap: 8px;
            background-color: rgba(10, 10, 26, 0.5);
            border: 2px solid rgba(56, 189, 248, 0.2);
            padding: 1rem;
            border-radius: 1rem;
            box-shadow: 0 0 25px rgba(56, 189, 248, 0.15), inset 0 0 10px rgba(56, 189, 248, 0.1);
        }

        .cell {
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 2.5rem;
            font-weight: 600;
            font-family: 'Fredoka', sans-serif;
            cursor: pointer;
            transition: background-color 0.3s, transform 0.2s, border-color 0.3s;
            user-select: none;
            width: 100%;
            aspect-ratio: 1 / 1;
            border-radius: 0.5rem;
            background-color: rgba(255, 255, 255, 0.03);
            border: 1px solid rgba(255, 255, 255, 0.1);
        }

        .cell:hover:not(.occupied) {
            background-color: rgba(56, 189, 248, 0.1);
            transform: scale(1.05);
            border-color: rgba(56, 189, 248, 0.5);
        }

        .cell.occupied {
            cursor: not-allowed;
        }

        .cell.x,
        .cell.o {
            animation: cell-pop-in 0.3s ease-out;
        }

        .cell.x {
            color: #38bdf8;
            text-shadow: 0 0 5px #38bdf8, 0 0 15px #38bdf8;
        }

        .cell.o {
            color: #f97316;
            text-shadow: 0 0 5px #f97316, 0 0 15px #f97316;
        }

        .cell.pending {
            opacity: 0.6;
            animation: pulse 1.5s infinite;
        }

        #modal {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: rgba(10, 10, 26, 0.8);
            backdrop-filter: blur(10px);
            display: none;
            align-items: center;
            justify-content: center;
            z-index: 2000;
        }

        #modal.show {
            display: flex;
        }

        .modal-content {
            background: linear-gradient(145deg, #1e1b4b, #171336);
            padding: 2.5rem;
            border-radius: 1rem;
            text-align: center;
            border: 1px solid rgba(56, 189, 248, 0.3);
            box-shadow: 0 0 30px rgba(56, 189, 248, 0.2);
            transform: scale(0.7);
            animation: modalPop 0.3s cubic-bezier(0.18, 0.89, 0.32, 1.28) forwards;
        }

        #turnIndicator {
            padding: 0.75rem 1.5rem;
            border-radius: 8px;
            background: rgba(10, 10, 26, 0.7);
            backdrop-filter: blur(8px);
            border: 1px solid rgba(255, 255, 255, 0.1);
            box-shadow: 0 0 15px rgba(0, 0, 0, 0.2);
            transition: all 0.3s ease;
        }

        #countdownTimer {
            animation: pulse 1s infinite;
            display: inline-block;
        }
    </style>
</head>

<body class="bg-[#0a0a1a]">
    <audio id="gameStartSound" src="audio.wav" preload="auto"></audio>
    <div id="loadingScreen"
        class="fade-in-up flex flex-col items-center justify-center min-h-screen bg-gradient-to-br from-[#4c1d95] via-[#6b21a8] to-[#1e1b4b] p-6 text-white">
        <div class="mb-8 logo-float">
            <svg width="120" height="120" viewBox="0 0 200 200" xmlns="http://www.w3.org/2000/svg">
                <defs>
                    <linearGradient id="logoGradient1" x1="0%" y1="0%" x2="100%" y2="100%">
                        <stop offset="0%" style="stop-color:#38bdf8" />
                        <stop offset="100%" style="stop-color:#6366f1" />
                    </linearGradient>
                    <linearGradient id="logoGradient2" x1="0%" y1="0%" x2="100%" y2="100%">
                        <stop offset="0%" style="stop-color:#fbbf24" />
                        <stop offset="100%" style="stop-color:#f97316" />
                    </linearGradient>
                    <linearGradient id="logoGradient3" x1="0%" y1="0%" x2="100%" y2="100%">
                        <stop offset="0%" style="stop-color:#ec4899" />
                        <stop offset="100%" style="stop-color:#d946ef" />
                    </linearGradient>
                </defs>
                <g transform="rotate(15 100 100)">
                    <path
                        d="M100 20 C144.18 20 180 55.82 180 100 C180 144.18 144.18 180 100 180 C55.82 180 20 144.18 20 100"
                        fill="none" stroke="url(#logoGradient1)" stroke-width="20" stroke-linecap="round" />
                    <path d="M100 20 C55.82 20 20 55.82 20 100 C20 144.18 55.82 180 100 180" fill="none"
                        stroke="url(#logoGradient2)" stroke-width="20" stroke-linecap="round"
                        transform="rotate(120 100 100)" />
                    <path d="M20 100 C20 55.82 55.82 20 100 20 C144.18 20 180 55.82 180 100" fill="none"
                        stroke="url(#logoGradient3)" stroke-width="20" stroke-linecap="round"
                        transform="rotate(240 100 100)" />
                    <circle cx="100" cy="100" r="25" fill="white" />
                </g>
            </svg>
        </div>
        <h1 class="font-fredoka text-4xl md:text-5xl font-bold text-center text-white tracking-wider text-glow mb-6">
            GamyDay Games
        </h1>
        <div class="w-full max-w-md mt-4 mb-8">
            <div class="h-4 bg-white/10 rounded-full overflow-hidden shadow-inner relative">
                <div id="progressBar"
                    class="h-full bg-gradient-to-r from-amber-400 to-orange-500 rounded-full transition-all duration-500 ease-out relative progress-bar-shine glow-bar"
                    style="width: 0%;"></div>
            </div>
            <p id="loadingMessage" class="text-center mt-4 text-sm text-gray-300 h-5 font-medium tracking-wide">
                Initializing...
            </p>
            <div id="waitTimerContainer" class="text-center mt-2 text-gray-300 h-10"></div>

        </div>
        <div class="absolute bottom-16 w-full text-center text-lg text-yellow-300 font-semibold animate-pulse">
            <p>Warning: ⚠️ Don't close tab — amount may be deducted.</p>
        </div>
        <div class="absolute bottom-6 text-center text-sm text-gray-400">
            powered by <span class="font-semibold text-white">GamyDay-Amrita</span>
        </div>
    </div>

    <div id="gameUI" class="flex flex-col items-center justify-center min-h-screen text-white p-4">
        <div class="w-full max-w-2xl mx-auto text-center mb-6">
            <h1 class="font-fredoka text-4xl md:text-5xl font-bold mb-2 text-white"
                style="text-shadow: 0 0 15px rgba(255,255,255,0.3);">7x7 Tic-Tac-Toe</h1>
            <p class="text-indigo-300">The first to get 4 in a row wins!</p>
        </div>

        <div class="flex justify-between w-full max-w-lg mb-4 text-lg">
            <div id="player1" class="font-semibold p-3 rounded-lg bg-black/20 border border-blue-400/30 shadow-lg">
                <span class="text-blue-400 text-glow-cyan">Player X:</span> <span id="player1Name">Waiting...</span>
            </div>
            <div id="player2" class="font-semibold p-3 rounded-lg bg-black/20 border border-orange-400/30 shadow-lg">
                <span class="text-orange-400 text-glow-orange">Player O:</span> <span id="player2Name">Waiting...</span>
            </div>
        </div>

        <div id="turnIndicator" class="text-center mb-6 text-xl font-medium">Waiting for players...</div>

        <div id="gameBoard" style="width: clamp(320px, 90vw, 600px); height: clamp(320px, 90vw, 600px);">
            <!-- Cells are generated by JS -->
        </div>

    </div>

    <div id="modal">
        <div class="modal-content">
            <h2 id="modalTitle" class="font-fredoka text-4xl font-bold text-white mb-4"></h2>
            <p id="modalMessage" class="text-xl text-gray-300 mb-8"></p>
            <div class="mt-4 text-lg text-yellow-300 font-semibold">
                <p>Warning: ⚠️ Don't refresh tab — amount may be deducted.</p>
            </div>
        </div>
    </div>

    <script>
        const DEBUG = false;
        const logger = {
            log: (...args) => DEBUG && console.log(...args),
            warn: (...args) => DEBUG && console.warn(...args),
            error: (...args) => DEBUG && console.error(...args),
        };

        const progressBar = document.getElementById('progressBar');
        const loadingMessage = document.getElementById('loadingMessage');
        const loadingScreen = document.getElementById('loadingScreen');
        const gameUI = document.getElementById('gameUI');
        const gameBoard = document.getElementById('gameBoard');
        const player1Name = document.getElementById('player1Name');
        const player2Name = document.getElementById('player2Name');
        const turnIndicator = document.getElementById('turnIndicator');
        const modal = document.getElementById('modal');
        const modalTitle = document.getElementById('modalTitle');
        const modalMessage = document.getElementById('modalMessage');

        const messages = [
            "Warming up the engines...",
            "Connecting to the game servers...",
            "Looking for a game room...",
            "Loading awesome assets...",
            "Waiting for players...",
            "Assembling pixels...",
            "Almost there, get ready!",
            "Game ready!"
        ];

        let progress = 0;
        let messageIndex = 0;
        let ws;
        let mySymbol = null;
        let currentTurn = null;
        let players = {};
        let gameStarted = false;
        let gameOver = false;
        let gameBoard7x7 = Array(7).fill().map(() => Array(7).fill(''));
        let loadingIntervals = {};
        let waitTimerInterval = null;
        let inactivityTimer = null;
        let clientInitiatedClose = false;

        window.onload = function () {
            const cleanUrl = window.location.origin + '/game-play/';
            window.history.replaceState({}, document.title, cleanUrl);
            initializeGameBoard();
            fetchWebSocketUrl();
            simulateLoading();
        };

        function simulateLoading() {
            const messageInterval = setInterval(() => {
                if (messageIndex < messages.length - 1) {
                    messageIndex++;
                    loadingMessage.innerText = messages[messageIndex];
                    if (messageIndex === 4) {
                        startWaitingTimer();
                    }
                }
            }, 1200);

            const progressInterval = setInterval(() => {
                if (progress < 80) {
                    progress += Math.random() * 2.5;
                    progressBar.style.width = Math.min(progress, 80) + '%';
                }
            }, 100);

            loadingIntervals = {messageInterval, progressInterval};
        }

        function startWaitingTimer() {
            const waitTimerContainer = document.getElementById('waitTimerContainer');
            if (!waitTimerContainer) return;

            waitTimerContainer.innerHTML = `
                <p class="text-sm">Estimated wait time: 5 minutes.</p>
                <p id="waitTimerDisplay" class="text-lg text-amber-300 font-semibold"></p>
            `;
            const timerDisplay = document.getElementById('waitTimerDisplay');

            let seconds = 300;

            const updateTimer = () => {
                const minutes = Math.floor(seconds / 60);
                const remainingSeconds = seconds % 60;
                if (timerDisplay) {
                    timerDisplay.innerText = `Time remaining: ${minutes}:${remainingSeconds.toString().padStart(2, '0')}`;
                }
            };

            updateTimer();

            waitTimerInterval = setInterval(() => {
                seconds--;
                updateTimer();

                if (seconds <= 0) {
                    clearInterval(waitTimerInterval);
                    const timerDisplay = document.getElementById('waitTimerDisplay');
                    if (timerDisplay) {
                        timerDisplay.innerText = 'Time is up! Redirecting Safely...';
                    }
                }
            }, 1000);
        }

        function stopWaitingTimer() {
            if (waitTimerInterval) {
                clearInterval(waitTimerInterval);
                const waitTimerContainer = document.getElementById('waitTimerContainer');
                if (waitTimerContainer) {
                    waitTimerContainer.innerHTML = '';
                }
            }
        }

        function startInactivityTimer() {
            stopInactivityTimer();

            let seconds = 30;

            const turnIndicator = document.getElementById('turnIndicator');
            const countdownSpan = document.createElement('span');
            countdownSpan.id = 'inactivityCountdown';
            countdownSpan.className = 'text-sm text-gray-400 ml-2';
            if (turnIndicator) {
                turnIndicator.appendChild(countdownSpan);
            }

            const updateCountdown = () => {
                if (countdownSpan) {
                    countdownSpan.innerText = `(${seconds}s)`;
                }
            };
            updateCountdown();

            inactivityTimer = setInterval(() => {
                seconds--;
                updateCountdown();
                if (seconds <= 0) {
                    stopInactivityTimer();
                    if (ws) {
                        clientInitiatedClose = true;
                        ws.close();
                    }
                    showErrorModal('You aborted the game due to inactivity.');
                }
            }, 1000);
        }

        function stopInactivityTimer() {
            if (inactivityTimer) {
                clearInterval(inactivityTimer);
                inactivityTimer = null;
            }
            const countdownSpan = document.getElementById('inactivityCountdown');
            if (countdownSpan) {
                countdownSpan.remove();
            }
        }

        async function fetchWebSocketUrl() {
            try {
                const response = await fetch('/game-play/room-service', {
                    method: 'GET',
                    credentials: 'include'
                });
                const data = await response.json();
                if (data.wsURL) {
                    connectWebSocket(data.wsURL);
                } else {
                    throw new Error('No WebSocket URL provided');
                }
            } catch (error) {
                logger.error('Error fetching WebSocket URL:', error);
                loadingMessage.innerText = 'Error connecting to server...';
            }
        }

        function connectWebSocket(wsUrl) {
            ws = new WebSocket(wsUrl);

            ws.onopen = () => {
                logger.log('WebSocket connected');
                loadingMessage.innerText = 'Connected! Waiting for players...';
            };

            ws.onmessage = (event) => {
                const data = JSON.parse(event.data);
                logger.log('Received message:', data);
                handleWebSocketMessage(data);
            };

            ws.onclose = () => {
                logger.log('WebSocket closed');
                if (clientInitiatedClose || gameOver) return;

                setTimeout(() => {
                    if (gameOver) {
                        return;
                    }

                    if (!gameStarted) {
                        showErrorModal('Failed to establish a stable connection to the game.');
                    } else {
                        showErrorModal('Connection lost. Please try again.');
                    }
                }, 10000);
            };

            ws.onerror = (error) => {
                logger.error('WebSocket error:', error);
                showErrorModal('Connection error. Please try again.');
            };
        }

        function handleWebSocketMessage(data) {
            switch (data.type) {
                case 'game_state':
                    handleInitialGameState(data.state);
                    break;
                case 'game_start':
                    handleGameStart(data);
                    break;
                case 'game_update':
                    handleGameUpdate(data.state);
                    break;
                case 'personal_state':
                    handlePersonalState(data.state);
                    break;
                case 'player_joined':
                    handlePlayerJoined(data);
                    break;
                case 'player_left':
                    handlePlayerLeft(data);
                    break;
                case 'game_over':
                    handleGameOver(data);
                    break;
                case 'pong':
                    break;
                case 'error':
                    showErrorModal(data.message);
                    break;
                default:
                    logger.warn('Unknown message type:', data.type);
            }
        }

        function handleGameStart(data) {
            logger.log('Game starting:', data);
            gameStarted = true;
            gameOver = false;
            stopWaitingTimer();

            if (data.state) {
                handleGameUpdate(data.state);
            }

            if (loadingScreen.style.display !== 'none') {
                finishLoading();
            }
        }

        function handleInitialGameState(state) {
            logger.log('Initial game state:', state);
            handleGameUpdate(state);
        }

        function handleGameUpdate(state) {
            logger.log('Game update:', state);

            if (state.currentTurn) {
                currentTurn = state.currentTurn.toUpperCase();
            }
            if (state.players) {
                players = state.players;
            }
            if (state.isGameOver !== undefined) {
                gameOver = state.isGameOver;
            }
            if (state.board) {
                updateBoard(state.board);
            }

            updatePlayerNames();
            updateTurnIndicator();
        }

        function handlePersonalState(state) {
            if (state.symbol) {
                mySymbol = state.symbol.toUpperCase();
            }
            logger.log('Personal state:', state);
            handleGameUpdate(state);
        }

        function handlePlayerJoined(data) {
            logger.log('Player joined:', data.username);
            if (data.state) {
                handleGameUpdate(data.state);
            }
        }

        function handlePlayerLeft(data) {
            logger.log('Player left:', data.username);
            if (data.state) {
                handleGameUpdate(data.state);
            }
        }

        function handleGameOver(data) {
            logger.log('Game over:', data);
            gameOver = true;
            stopInactivityTimer();
            handleGameUpdate(data.state);
            showGameEndModal(data);
        }

        function updatePlayerNames() {
            const player1Text = players['X'] || 'Waiting...';
            const player2Text = players['O'] || 'Waiting...';

            player1Name.innerText = player1Text;
            player2Name.innerText = player2Text;
        }

        function updateTurnIndicator() {
            if (gameOver) {
                turnIndicator.innerText = 'Game Over';
                turnIndicator.className = 'text-center mb-6 text-xl font-medium text-gray-400';
                return;
            }

            if (!currentTurn || !mySymbol) {
                turnIndicator.innerText = 'Waiting for game to start...';
                turnIndicator.className = 'text-center mb-6 text-xl font-medium text-gray-400';
                return;
            }

            const isMyTurn = currentTurn === mySymbol;
            const currentPlayerName = players[currentTurn] || 'Unknown';

            if (isMyTurn) {
                turnIndicator.innerText = 'Your turn!';
                turnIndicator.className = 'text-center mb-6 text-xl font-medium text-blue-400 text-glow-cyan';
                startInactivityTimer();
            } else {
                turnIndicator.innerText = `Waiting for ${currentPlayerName}...`;
                turnIndicator.className = 'text-center mb-6 text-xl font-medium text-orange-400 text-glow-orange';
                stopInactivityTimer();
            }
        }

        function finishLoading() {
            progress = 100;
            progressBar.style.width = '100%';
            loadingMessage.innerText = messages[messages.length - 1];
            stopWaitingTimer();

            if (loadingIntervals.messageInterval) clearInterval(loadingIntervals.messageInterval);
            if (loadingIntervals.progressInterval) clearInterval(loadingIntervals.progressInterval);

            const gameStartSound = document.getElementById('gameStartSound');
            if (gameStartSound) {
                gameStartSound.play().catch(e => logger.warn("Could not play audio:", e));
            }

            setTimeout(() => {
                loadingScreen.classList.add('fade-out');
                setTimeout(() => {
                    loadingScreen.style.display = 'none';
                    document.body.style.overflow = 'auto';
                    gameUI.classList.add('active');
                }, 500);
            }, 1000);
        }

        function initializeGameBoard() {
            gameBoard.innerHTML = '';
            for (let i = 0; i < 7; i++) {
                for (let j = 0; j < 7; j++) {
                    const cell = document.createElement('div');
                    cell.className = 'cell';
                    cell.dataset.row = i;
                    cell.dataset.col = j;
                    cell.addEventListener('click', () => handleCellClick(i, j));
                    gameBoard.appendChild(cell);
                }
            }
            logger.log('Game board initialized');
        }

        function handleCellClick(row, col) {
            logger.log(`Cell clicked: [${row}, ${col}]`);

            const isMyTurn = currentTurn === mySymbol;
            const isCellOccupied = gameBoard7x7[row] && gameBoard7x7[row][col] !== '';

            if (gameOver || !isMyTurn || isCellOccupied) {
                logger.warn('Click ignored:', {gameOver, isMyTurn, isCellOccupied});
                return;
            }

            stopInactivityTimer();

            const cell = gameBoard.querySelector(`[data-row="${row}"][data-col="${col}"]`);
            if (cell) {
                cell.innerText = mySymbol;
                cell.className = `cell ${mySymbol.toLowerCase()} occupied pending`;
                gameBoard7x7[row][col] = mySymbol;

                const opponentSymbol = mySymbol === 'X' ? 'O' : 'X';
                currentTurn = opponentSymbol;
                updateTurnIndicator();
            }

            if (ws && ws.readyState === WebSocket.OPEN) {
                const action = {type: 'game_action', action: {row, col}};
                logger.log('Sending action:', action);
                ws.send(JSON.stringify(action));
            } else {
                logger.error('WebSocket not ready:', ws ? ws.readyState : 'null');
                gameBoard7x7[row][col] = '';
                updateBoard(gameBoard7x7);
            }
        }

        function updateBoard(board) {
            if (!board || !Array.isArray(board)) {
                logger.warn('Invalid board data:', board);
                return;
            }

            logger.log('Updating board from server state');
            gameBoard7x7 = board;

            for (let i = 0; i < 7; i++) {
                for (let j = 0; j < 7; j++) {
                    const cell = gameBoard.querySelector(`[data-row="${i}"][data-col="${j}"]`);
                    if (cell) {
                        const symbol = board[i] && board[i][j] ? board[i][j] : '';
                        const displaySymbol = symbol.toUpperCase();

                        cell.innerText = displaySymbol;
                        cell.className = 'cell';
                        if (displaySymbol) {
                            cell.classList.add(displaySymbol.toLowerCase(), 'occupied');
                        }
                    }
                }
            }
        }

        function showModalAndRedirect(title, message, isWinner) {
            if (modal.classList.contains('show')) return;

            modalTitle.innerText = title;
            modalMessage.innerText = message;
            modal.classList.add('show');

            if (isWinner) {
                confetti({
                    particleCount: 150,
                    spread: 100,
                    origin: {
                        y: 0.6
                    }
                });
            }

            redirect();
        }

        function showGameEndModal(data) {
            const {
                winner,
                reason,
                message,
                detailedMessage
            } = data;

            let modalTitleText = message || 'Game Over';
            const isWinner = (winner === mySymbol && reason === 'COMPLETED');

            if (reason === 'COMPLETED') {
                if (isWinner) {
                    modalTitleText = 'You Win!';
                } else if (winner) {
                    modalTitleText = 'You Lose!';
                } else {
                    modalTitleText = "It's a Draw!";
                }
            }

            showModalAndRedirect(modalTitleText, detailedMessage || 'The game has ended.', isWinner);
        }

        function showErrorModal(message) {
            showModalAndRedirect('Game Error', message, false);
        }

        function redirect() {
            fetch('/game-play/get-return-url', {credentials: 'include'})
                .then(res => res.json())
                .then(({returnUrl}) => {
                    let countdown = 5;
                    modalMessage.innerHTML += `<br><br><div class="mt-4"><span id="countdownTimer" class="text-sm text-amber-300 font-medium">Redirecting in ${countdown} seconds...</span></div>`;

                    const timer = setInterval(() => {
                        countdown--;
                        const timerEl = document.getElementById('countdownTimer');
                        if (timerEl) {
                            if (countdown <= 0) {
                                clearInterval(timer);
                                window.location.href = returnUrl;
                            } else {
                                timerEl.innerText = `Redirecting in ${countdown} seconds...`;
                            }
                        } else {
                            clearInterval(timer);
                        }
                    }, 1000);
                })
                .catch(err => {
                    logger.error('Failed to fetch return URL:', err);
                });
        }

        setInterval(() => {
            if (ws && ws.readyState === WebSocket.OPEN) {
                ws.send(JSON.stringify({type: 'ping'}));
            }
        }, 30000);

    </script>
</body>

</html>
